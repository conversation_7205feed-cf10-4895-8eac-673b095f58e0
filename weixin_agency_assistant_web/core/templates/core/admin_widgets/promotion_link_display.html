{% load static %}

<style>
    .promotion-container {
        position: relative;
        display: inline-block;
    }

    .promotion-link {
        background: #007cba;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        text-decoration: none;
        font-size: 12px;
    }

    .promotion-link:hover {
        background: #005a87;
        color: white;
        text-decoration: none;
    }

    .promotion-popup {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        width: 200px;
        margin-top: 5px;
        text-align: center;
    }

    /* 只有表格最后3行的popup向上显示 */
    tbody tr:nth-last-child(-n+3) .promotion-popup {
        top: auto;
        bottom: 100%;
        margin-top: 0;
        margin-bottom: 5px;
    }

    .promotion-container:hover .promotion-popup {
        display: block;
    }

    .qr-code {
        margin-bottom: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .qr-code img {
        display: block;
        width: 200px;
        height: 200px;
    }

    .link-text {
        font-size: 11px;
        word-break: break-all;
        color: #666;
        margin-top: 5px;
    }
</style>

<div class="promotion-container"
     data-link="{{ obj.item_link }}"
>
    <a href="#" class="promotion-link" onclick="copyPromotionLink(this); return false;">{{ obj.item_link }}</a>

    <div class="promotion-popup">
        <div class="qr-code">
            <img src="data:image/png;base64,{{ obj.qrcode_link_base64_data }}"  alt="QRCode"/>
        </div>
        <div class="link-text">{{ obj.item_link|truncatechars:50 }}</div>
    </div>
</div>

<script src="{% static 'js/qrcode.min.js' %}"></script>
<script>
    // 复制推广链接到剪贴板
    function copyPromotionLink(element) {
        const container = element.closest('.promotion-container');
        const link = container.getAttribute('data-link');

        if (navigator.clipboard) {
            navigator.clipboard.writeText(link).then(function () {
                showCopySuccess();
            }).catch(function () {
                fallbackCopy(link);
            });
        } else {
            fallbackCopy(link);
        }
    }

    // 备用复制方法
    function fallbackCopy(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.select();

        try {
            document.execCommand('copy');
            showCopySuccess();
        } catch (err) {
            alert('复制失败，请手动复制链接');
        }

        document.body.removeChild(textArea);
    }

    // 显示复制成功提示
    function showCopySuccess() {
        const successDiv = document.createElement('div');
        successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10001;
    `;
        successDiv.textContent = '✓ 推广链接已复制到剪贴板';
        document.body.appendChild(successDiv);

        setTimeout(function () {
            if (document.body.contains(successDiv)) {
                document.body.removeChild(successDiv);
            }
        }, 3000);
    }
</script>
