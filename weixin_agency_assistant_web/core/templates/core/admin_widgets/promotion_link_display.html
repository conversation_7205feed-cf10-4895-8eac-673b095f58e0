{% load static %}

<style>
    .promotion-container {
        position: relative;
        display: inline-block;
    }

    .promotion-link {
        background: #007cba;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        text-decoration: none;
        font-size: 12px;
        cursor: pointer;
    }

    .promotion-link:hover {
        background: #005a87;
        color: white;
        text-decoration: none;
    }

    .promotion-popup {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        width: 220px;
        margin-top: 5px;
        text-align: center;
    }

    /* 只有表格最后3行的popup向上显示 */
    tbody tr:nth-last-child(-n+3) .promotion-popup {
        top: auto;
        bottom: 100%;
        margin-top: 0;
        margin-bottom: 5px;
    }

    .qr-code {
        margin-bottom: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    .qr-code canvas {
        display: block;
        width: 200px;
        height: 200px;
    }

    .link-text {
        font-size: 11px;
        word-break: break-all;
        color: #666;
        margin-top: 5px;
    }

    .close-popup {
        position: absolute;
        top: 5px;
        right: 8px;
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        color: #999;
        line-height: 1;
    }

    .close-popup:hover {
        color: #333;
    }

    .copy-button {
        background: #28a745;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 11px;
        cursor: pointer;
        margin-top: 5px;
    }

    .copy-button:hover {
        background: #218838;
    }
</style>

<div class="promotion-container"
     data-link="{{ obj.qrcode_link }}"
     data-item-link="{{ obj.item_link }}"
     data-product-id="{{ obj.id }}"
>
    <a href="#" class="promotion-link" onclick="showPromotionPopup(this); return false;">推广链接</a>

    <div class="promotion-popup">
        <button class="close-popup" onclick="hidePromotionPopup(this)">&times;</button>
        <div class="qr-code qrcode-container"></div>
        <div class="link-text">{{ obj.item_link|truncatechars:50 }}</div>
        <button class="copy-button" onclick="copyPromotionLinkFromPopup(this)">复制链接</button>
    </div>
</div>

<script src="{% static 'js/qrcode.min.js' %}"></script>
<script>
    // 显示推广弹窗并生成二维码
    function showPromotionPopup(element) {
        const container = element.closest('.promotion-container');
        const popup = container.querySelector('.promotion-popup');
        const qrcodeContainer = popup.querySelector('.qrcode-container');
        const qrcodeLink = container.getAttribute('data-link');

        // 清空之前的二维码
        qrcodeContainer.innerHTML = '';

        // 生成新的二维码
        if (qrcodeLink && typeof QRCode !== 'undefined') {
            try {
                new QRCode(qrcodeContainer, {
                    text: qrcodeLink,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.L
                });
            } catch (error) {
                console.error('生成二维码失败:', error);
                qrcodeContainer.innerHTML = '<div style="color: #999; padding: 20px;">二维码生成失败</div>';
            }
        } else {
            qrcodeContainer.innerHTML = '<div style="color: #999; padding: 20px;">无法生成二维码</div>';
        }

        // 显示弹窗
        popup.style.display = 'block';

        // 点击其他地方关闭弹窗
        setTimeout(() => {
            document.addEventListener('click', function closePopupOnOutsideClick(e) {
                if (!container.contains(e.target)) {
                    popup.style.display = 'none';
                    document.removeEventListener('click', closePopupOnOutsideClick);
                }
            });
        }, 100);
    }

    // 隐藏推广弹窗
    function hidePromotionPopup(element) {
        const popup = element.closest('.promotion-popup');
        popup.style.display = 'none';
    }

    // 从弹窗复制推广链接到剪贴板
    function copyPromotionLinkFromPopup(element) {
        const container = element.closest('.promotion-container');
        const link = container.getAttribute('data-item-link');

        if (navigator.clipboard) {
            navigator.clipboard.writeText(link).then(function () {
                showCopySuccess();
            }).catch(function () {
                fallbackCopy(link);
            });
        } else {
            fallbackCopy(link);
        }
    }

    // 备用复制方法
    function fallbackCopy(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.select();

        try {
            document.execCommand('copy');
            showCopySuccess();
        } catch (err) {
            alert('复制失败，请手动复制链接');
        }

        document.body.removeChild(textArea);
    }

    // 显示复制成功提示
    function showCopySuccess() {
        const successDiv = document.createElement('div');
        successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10001;
    `;
        successDiv.textContent = '✓ 推广链接已复制到剪贴板';
        document.body.appendChild(successDiv);

        setTimeout(function () {
            if (document.body.contains(successDiv)) {
                document.body.removeChild(successDiv);
            }
        }, 3000);
    }
</script>
